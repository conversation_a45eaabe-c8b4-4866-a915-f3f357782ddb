<template>
  <div class="table-responsive">
    <table class="data-table">
      <thead>
        <tr>
          <th @click="requestSort('reportId')" :class="getSortClass('reportId')">ID</th>
          <th @click="requestSort('reportType')" :class="getSortClass('reportType')">Type</th>
          <th @click="requestSort('reportSeverity')" :class="getSortClass('reportSeverity')">Severity</th>
          <th @click="requestSort('serverReceivedAt')" :class="getSortClass('serverReceivedAt')">Received At</th>
          <th>User</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="report in reports" :key="report.reportId" @click="$emit('view-report', report)" class="table-row">
          <td class="report-id">{{ report.reportId?.substring(0, 8) }}...</td>
          <td>{{ formatType(report.reportType) }}</td>
          <td>
            <span :style="{ color: getSeverityColor(report.reportSeverity) }" class="severity-badge">
              {{ formatSeverity(report.reportSeverity) }}
            </span>
          </td>
          <td>{{ formatDate(report.serverReceivedAt) }}</td>
          <td>Anonymous</td>
          <td class="actions-cell">
            <button @click.stop="$emit('view-report', report)" class="btn btn-sm btn-ghost">View</button>
            <button 
              @click.stop="report.reportId && $emit('download-report', report.reportId)" 
              class="btn btn-sm btn-ghost" 
              :disabled="!report.reportId"
            >
              Download
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
import { format } from 'date-fns';

interface ParsedReport {
  reportId?: string;
  reportType?: string;
  reportSeverity?: string;
  serverReceivedAt?: string;
  reportDescription?: string;
}

interface ReportSortPayload {
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

const SEVERITY_COLORS = {
  low: '#10b981',
  medium: '#f59e0b', 
  high: '#ef4444',
  critical: '#dc2626'
};

defineOptions({
  name: 'DebugReportTable'
});

const props = defineProps<{
  reports: ParsedReport[];
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}>();

const emit = defineEmits<{
  'view-report': [report: ParsedReport];
  'download-report': [id: string];
  'sort': [criteria: ReportSortPayload];
}>();

function formatType(type?: string): string {
  if (!type) return 'N/A';
  return type.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
}

function formatSeverity(severity?: string): string {
  if (!severity) return 'N/A';
  return severity.charAt(0).toUpperCase() + severity.slice(1);
}

function formatDate(dateString?: string): string {
  return dateString ? format(new Date(dateString), 'MMM dd, yyyy HH:mm') : 'N/A';
}

function getSeverityColor(severity?: string): string {
  return severity ? (SEVERITY_COLORS[severity as keyof typeof SEVERITY_COLORS] || '#666') : '#666';
}

function requestSort(field: string) {
  let newOrder: 'asc' | 'desc' = 'desc';
  if (props.sortBy === field) {
    newOrder = props.sortOrder === 'asc' ? 'desc' : 'asc';
  }
  emit('sort', { sortBy: field, sortOrder: newOrder });
}

function getSortClass(field: string): string {
  if (props.sortBy !== field) return 'sortable';
  return `sortable sorted-${props.sortOrder}`;
}
</script>

<style scoped>
.table-responsive {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-sm, 0.875rem);
}

.data-table th, 
.data-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--border-base, #e0e0e0);
}

.data-table th {
  background-color: var(--bg-surface-hover, #f8f9fa);
  font-weight: var(--font-weight-semibold, 600);
  cursor: pointer;
  user-select: none;
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table th.sortable:hover {
  background-color: var(--gray-200, #e2e8f0);
}

.data-table th.sorted-asc::after { 
  content: ' ▲'; 
  color: var(--primary-500, #3b82f6);
}

.data-table th.sorted-desc::after { 
  content: ' ▼';
  color: var(--primary-500, #3b82f6);
}

.table-row {
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.table-row:hover {
  background-color: var(--bg-surface-hover, #f8f9fa);
}

.report-id {
  font-family: var(--font-family-mono, monospace);
  font-size: var(--font-size-xs, 0.75rem);
}

.severity-badge {
  font-weight: var(--font-weight-semibold, 600);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-full, 9999px);
  background-color: currentColor;
  color: white !important;
  font-size: var(--font-size-xs, 0.75rem);
}

.actions-cell {
  display: flex;
  gap: 0.5rem;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md, 6px);
  border: 1px solid transparent;
  cursor: pointer;
  font-weight: var(--font-weight-semibold, 600);
  transition: all 0.2s ease-in-out;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: var(--font-size-xs, 0.75rem);
}

.btn-ghost {
  background: transparent;
  color: var(--primary-500, #3b82f6);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--primary-50, #eff6ff);
}

.btn-ghost:disabled {
  color: var(--text-quaternary, #94a3b8);
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .data-table th,
  .data-table td {
    padding: 0.5rem;
  }

  .actions-cell {
    flex-direction: column;
    gap: 0.25rem;
  }
}

/* ===== DARK MODE SUPPORT ===== */

[data-theme="dark"] .data-table th {
  background-color: var(--bg-surface-hover-dark, #374151);
  color: var(--text-primary-dark, #f9fafb);
  border-bottom-color: var(--border-base-dark, #4b5563);
}

[data-theme="dark"] .data-table th.sortable:hover {
  background-color: var(--bg-hover-dark, #4b5563);
}

[data-theme="dark"] .data-table th.sorted-asc::after,
[data-theme="dark"] .data-table th.sorted-desc::after {
  color: var(--primary-400, #60a5fa);
}

[data-theme="dark"] .data-table td {
  border-bottom-color: var(--border-base-dark, #4b5563);
  color: var(--text-primary-dark, #f9fafb);
}

[data-theme="dark"] .table-row:hover {
  background-color: var(--bg-surface-hover-dark, #374151);
}

[data-theme="dark"] .report-id {
  color: var(--text-secondary-dark, #d1d5db);
}

[data-theme="dark"] .btn-ghost {
  color: var(--primary-400, #60a5fa);
}

[data-theme="dark"] .btn-ghost:hover:not(:disabled) {
  background-color: var(--primary-900, #1e3a8a);
}

[data-theme="dark"] .btn-ghost:disabled {
  color: var(--text-quaternary-dark, #6b7280);
}
</style>